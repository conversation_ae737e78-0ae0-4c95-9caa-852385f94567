import { Users, DollarSign, UserPlus, UserMinus } from 'lucide-react';
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import StatsCard from './components/StatsCard';
import HorizontalBarChart from '../../components/charts/HorizontalBarChart';
import { Button } from '../../components/Button';
import InsightsCard from './components/InsightsCard';
import UserTable from './components/UserTable';
import AddUserModal from './components/AddUserModal';
import AddBulkUserModal from './components/AddBulkUserModal';
import { userRoleData } from '../../mockData/mockData';
import Breadcrumb from '../../components/Breadcrumb/Breadcrumb';
import { getUsers } from '../../api/userManagementApis';
import Loader from '../../components/Loader';
import ErrorPlaceholder from '../../components/ErrorPlaceholder';

const UserManagement = () => {
  const [isAddUserModalOpen, setIsAddUserModalOpen] = useState(false);
  const [isAddBulkUserModalOpen, setIsAddBulkUserModalOpen] = useState(false);

  const {
    data: usersData,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: ['users'],
    queryFn: getUsers,
  });

  const handleAddUser = (userData: any) => {
    console.log('New user data:', userData);
    refetch();
  };

  const handleAddBulkUser = (file: File) => {
    console.log('CSV file uploaded successfully:', file.name, file.size);
    refetch();
  };

  return (
    <main className="p-6 bg-gray-50 space-y-8">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-500">User Management</h1>
          <Breadcrumb pageName="User Management" />
        </div>
        <div className="flex flex-col sm:flex-row gap-3 text-gray-200 font-semibold">
          <Button
            className="bg-teal-700 px-4 py-3 rounded-md text-sm"
            onClick={() => setIsAddBulkUserModalOpen(true)}
          >
            + Add Bulk User
          </Button>
          <Button
            className="brand-gradient px-4 py-3 rounded-md text-sm"
            onClick={() => setIsAddUserModalOpen(true)}
          >
            + Add User
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Active Users"
          value={250}
          change="+10%"
          changeColor="text-green-600"
          iconBg="bg-blue-100"
          iconColor="text-blue-600"
          icon={<Users />}
        />
        <StatsCard
          title="Average Expenses per User"
          value="$1,100"
          change="+8%"
          changeColor="text-green-600"
          iconBg="bg-green-100"
          iconColor="text-green-600"
          icon={<DollarSign />}
        />
        <StatsCard
          title="New Users This Month"
          value={75}
          change="-5%"
          changeColor="text-red-600"
          iconBg="bg-purple-100"
          iconColor="text-purple-600"
          icon={<UserPlus />}
        />
        <StatsCard
          title="Inactive Users"
          value={3}
          change="-2%"
          changeColor="text-red-600"
          iconBg="bg-red-100"
          iconColor="text-red-600"
          icon={<UserMinus />}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Users by Role</h3>
          <div className="min-h-[280px]">
            <HorizontalBarChart
              data={userRoleData.map((item) => ({
                label: item.role,
                value: item.count,
              }))}
            />
          </div>
        </div>
        <InsightsCard />
      </div>

      <div className="bg-white rounded-lg shadow-sm p-6">
        {isLoading ? (
          <Loader size="md" />
        ) : isError ? (
          <ErrorPlaceholder
            title="Failed to load users"
            message={error?.message || 'Unable to fetch users data. Please try again.'}
            onRetry={refetch}
          />
        ) : (
          <UserTable title="All Users" users={usersData?.results || []} />
        )}
      </div>

      <AddUserModal
        isOpen={isAddUserModalOpen}
        onClose={() => setIsAddUserModalOpen(false)}
        onSubmit={handleAddUser}
      />
      <AddBulkUserModal
        isOpen={isAddBulkUserModalOpen}
        onClose={() => setIsAddBulkUserModalOpen(false)}
        onSubmit={handleAddBulkUser}
      />
    </main>
  );
};

export default UserManagement;
